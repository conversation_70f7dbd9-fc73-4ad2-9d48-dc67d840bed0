### ---------------------------------------------------------------------------------------------
### N8N使用量监控测试
### 测试N8N API调用和使用量提取功能
### ---------------------------------------------------------------------------------------------

### Environment / variables
@baseUrl = http://localhost:18019
@n8nBaseUrl = https://n.test52dzhp.com
@n8nApiKey = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.w3bDEw0SzBIIxslrbu56oenviz7i7guZwxf9xpUhR6Y
@workflowId = zDlbOldGMeMh5img

### ---------------------------------------------------------------------------------------------
### Health check: GET /
GET {{baseUrl}}/

### ---------------------------------------------------------------------------------------------
### 测试N8N API - 获取执行列表
GET {{n8nBaseUrl}}/api/v1/executions?workflowId={{workflowId}}&status=success&includeData=false&limit=5
X-N8N-API-KEY: {{n8nApiKey}}
Accept: application/json

### ---------------------------------------------------------------------------------------------
### 测试N8N API - 获取单个执行详情
GET {{n8nBaseUrl}}/api/v1/executions/48170?includeData=true
X-N8N-API-KEY: {{n8nApiKey}}
Accept: application/json

### ---------------------------------------------------------------------------------------------
### 测试LLM使用量记录 - 单条记录
POST {{baseUrl}}/api/llm/usage
Content-Type: application/json

{
  "modelName": "google/gemini-2.0-flash-001",
  "promptTokens": 120,
  "completionTokens": 250,
  "totalTokens": 370,
  "callBy": "n8n-workflow",
  "userId": "**********",
  "requestId": "e50fa234-4348-44bc-b2b8-3df4ab1a92c3_1006"
}

### ---------------------------------------------------------------------------------------------
### 测试LLM使用量记录 - 批量记录
POST {{baseUrl}}/api/llm/usage/batch
Content-Type: application/json

[
  {
    "modelName": "google/gemini-2.0-flash-001",
    "promptTokens": 100,
    "completionTokens": 200,
    "totalTokens": 300,
    "callBy": "n8n-workflow",
    "userId": "**********",
    "requestId": "test-request-1"
  },
  {
    "modelName": "google/gemini-2.0-flash-001",
    "promptTokens": 80,
    "completionTokens": 150,
    "totalTokens": 230,
    "callBy": "n8n-workflow",
    "userId": "**********",
    "requestId": "test-request-2"
  }
]

### ---------------------------------------------------------------------------------------------
### 注意事项
### 1. N8N使用量监控服务会在应用启动时自动开始运行
### 2. 服务每分钟检查一次最近的执行记录
### 3. 提取的使用量信息会自动记录到llm_usage_log表中
### 4. 可以通过应用日志查看监控服务的运行状态
### 5. 正则表达式会匹配以下格式的数据：
###    - "tokenUsage": {"completionTokens": XXXX, "promptTokens": XXXX, "totalTokens": XXXX}
###    - "request_id": "e50fa234-4348-44bc-b2b8-3df4ab1a92c3_1006"
###    - "user_id": "**********"
###    - "llm_model_name": "google/gemini-2.0-flash-001"
