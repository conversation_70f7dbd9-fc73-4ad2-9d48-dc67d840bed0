# N8N使用量监控服务

## 概述

N8N使用量监控服务是一个自动化的后台服务，用于定期检查N8N工作流执行记录，提取其中的LLM使用量信息，并将这些信息记录到数据库中。

## 功能特性

### 1. 定时监控
- 每分钟自动执行一次检查
- 检查过去一分钟内完成的N8N工作流执行记录
- 使用分页查询确保不遗漏任何执行记录

### 2. 数据提取
使用正则表达式从N8N执行数据中提取以下信息：
- **Token使用量**: `"tokenUsage": {"completionTokens": XXXX, "promptTokens": XXXX, "totalTokens": XXXX}`
- **请求ID**: `"request_id": "e50fa234-4348-44bc-b2b8-3df4ab1a92c3_1006"`
- **用户ID**: `"user_id": "3301064155"`
- **模型名称**: `"llm_model_name": "google/gemini-2.0-flash-001"`

### 3. 数据库记录
提取的数据会自动记录到 `llm_usage_log` 表中，包含以下字段：
- `model_name`: "n8n-llm"
- `prompt_tokens`: 提示词token数量
- `completion_tokens`: 补全token数量
- `total_tokens`: 总token数量
- `timestamp`: 记录时间戳
- `call_by`: "n8n-workflow"
- `user_id`: 用户ID
- `request_id`: 请求ID

## 配置说明

### application.yaml配置
```yaml
api:
  n8n:
    url: "https://n.test52dzhp.com"
    apiKey: "your-n8n-api-key"
    workflowId: "your-workflow-id"
```

### 配置参数说明
- `url`: N8N服务器地址
- `apiKey`: N8N API密钥，用于身份验证
- `workflowId`: 要监控的工作流ID

## 工作流程

1. **启动监控**: 应用启动时自动启动N8N使用量监控服务
2. **定时检查**: 每分钟执行一次检查任务
3. **获取执行列表**: 调用N8N API获取最近一分钟内的执行记录
4. **分页处理**: 如果有多页数据，自动处理分页查询
5. **获取详细数据**: 对每个执行记录获取详细的执行数据
6. **正则匹配**: 使用正则表达式提取token使用量、请求ID和用户ID
7. **数据记录**: 将提取的数据记录到数据库
8. **日志记录**: 记录处理过程和结果

## API接口

### N8N API调用
- **获取执行列表**: `GET /api/v1/executions?workflowId={id}&status=success&includeData=false&limit=100`
- **获取执行详情**: `GET /api/v1/executions/{id}?includeData=true`

### 认证方式
使用Header认证：`X-N8N-API-KEY: {your-api-key}`

## 日志监控

### 日志级别
- **INFO**: 服务启动/停止、处理结果统计
- **DEBUG**: 详细的处理过程、API调用信息
- **WARN**: 数据缺失、解析失败等警告
- **ERROR**: API调用失败、数据库操作失败等错误

### 关键日志示例
```
[INFO] 启动N8N使用量监控服务，每分钟检查一次
[INFO] 发现 3 个最近执行记录，开始提取使用量信息
[INFO] 记录LLM使用量: executionId=48170, recordId=123, userId=3301064155, requestId=e50fa234-4348-44bc-b2b8-3df4ab1a92c3_1006, tokens=370
[INFO] 执行记录 48170 处理完成，共记录 2 条LLM使用量
```

## 错误处理

### 网络错误
- API调用超时（30秒）
- 网络连接失败
- 认证失败

### 数据错误
- JSON解析失败
- 正则匹配失败
- 数据库写入失败

### 恢复机制
- 单个执行记录处理失败不影响其他记录
- 网络错误会在下次检查时重试
- 服务异常会记录错误日志但不会停止监控

## 性能考虑

### 资源使用
- 使用单独的线程池执行定时任务
- HTTP客户端复用连接
- 数据库连接池管理

### 优化策略
- 分页查询避免一次性加载大量数据
- 时间戳比较提前终止不必要的查询
- 异步处理提高并发性能

## 测试

### 手动测试
使用 `src/test/N8NUsage.http` 文件进行手动测试：
- 测试N8N API连接
- 测试数据提取功能
- 测试数据库记录功能

### 监控验证
- 检查应用日志确认服务正常运行
- 查询数据库确认数据正确记录
- 监控API调用频率和响应时间

## 故障排除

### 常见问题
1. **API密钥无效**: 检查配置文件中的apiKey是否正确
2. **工作流ID错误**: 确认workflowId配置正确
3. **网络连接问题**: 检查N8N服务器是否可访问
4. **数据库连接问题**: 确认MySQL连接配置正确
5. **正则匹配失败**: 检查N8N返回数据格式是否发生变化

### 调试步骤
1. 检查应用启动日志
2. 查看N8N API调用日志
3. 验证正则表达式匹配结果
4. 检查数据库记录情况
5. 分析错误日志定位问题
